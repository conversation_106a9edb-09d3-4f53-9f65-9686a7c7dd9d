<?xml version="1.0" encoding="UTF-8"?>
<Workflow xmlns="http://soap.sforce.com/2006/04/metadata">
    <fieldUpdates>
        <fullName>Update_FlagofSendtoSAP_Order</fullName>
        <field>Flag_of_Send_to_SAP__c</field>
        <literalValue>0</literalValue>
        <name>Update FlagofSendtoSAP Order</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Update_Order_Start_Date_On_Activation</fullName>
        <field>EffectiveDate</field>
        <formula>ActivatedDate</formula>
        <name>Update Order Start Date On Activation</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Update_Order_Start_Date_On_Creation</fullName>
        <description>This action updates the Order start date with the order creation date</description>
        <field>EffectiveDate</field>
        <formula>TODAY()</formula>
        <name>Update Order Start Date On Creation</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <rules>
        <fullName>JJ JPN Order Submit for FlagofSendtoSAP</fullName>
        <actions>
            <name>Update_FlagofSendtoSAP_Order</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Order.Flag_of_Send_to_SAP__c</field>
            <operation>equals</operation>
            <value>1</value>
        </criteriaItems>
        <description>When new Order record is created, this Workflow Rule runs.</description>
        <triggerType>onCreateOnly</triggerType>
    </rules>
    <rules>
        <fullName>Update Order Start Date On Activation</fullName>
        <actions>
            <name>Update_Order_Start_Date_On_Activation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>false</active>
        <criteriaItems>
            <field>Order.Status</field>
            <operation>equals</operation>
            <value>Activated</value>
        </criteriaItems>
        <description>This rule updates the Order start date with the Activated Date on activating the order</description>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Update Order Start Date On Creation</fullName>
        <actions>
            <name>Update_Order_Start_Date_On_Creation</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Order.Status</field>
            <operation>equals</operation>
            <value>Draft</value>
        </criteriaItems>
        <description>This rule updates the Order Start Date with Created Date on Order creation.</description>
        <triggerType>onCreateOnly</triggerType>
    </rules>
</Workflow>
