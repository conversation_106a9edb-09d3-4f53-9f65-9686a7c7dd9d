({
    calDestroy:function(component, event, helper){
        //Anil <PERSON>_ Start
        helper.initializelookups(component);
        //Anil M _ End
        $("#calendar").fullCalendar('destroy');
        helper.fetchEvents(component, event, helper);
    },
    
    fetchPastEventRecords: function(component, accountId) {
        let action = component.get("c.getPastEventRecords");
        action.setParams({
            accountId: accountId
        });
        action.setCallback(this, function(response) {
            let state = response.getState();
            if (state === "SUCCESS") {
                let pastEvents = response.getReturnValue();

                // Always clear previous phase values first to prevent cross-account contamination
                component.set("v.ast_Phase", null);
                component.set("v.mf_Phase", null);
                component.set("v.astRecommendTarget", []);
                component.set("v.mfRecommendTarget", []);

                // Clear event phase values
                let tempEvent = component.get("v.event");
                tempEvent.JJ_JPN_AST_Phase__c = null;
                tempEvent.JJ_JPN_MF_Phase__c = null;
                tempEvent.JJ_JPN_AST_Recommend_Target__c = null;
                tempEvent.JJ_JPN_MF_Recommend_Target__c = null;
                component.set("v.event", tempEvent);

                if (pastEvents && pastEvents.length > 0) {
                    let pastEvent = pastEvents[0];
                    component.set("v.maxPhase", pastEvent.No_of_New_Wearer_record__c);
                    component.set("v.maxRecommendedTarget", pastEvent.SW_Clrvw_Best_Visit_Times__c);
                    component.set("v.teikibinPhase",pastEvent.SW_Clrvw_RelatedCallId__c);
                    component.set("v.teikibinRecommendedTarget",pastEvent.SW_MyopiaStageOptions__c);

                    component.set("v.event.No_of_New_Wearer_record__c", pastEvent.No_of_New_Wearer_record__c);
                    component.set("v.event.SW_Clrvw_Best_Visit_Times__c", pastEvent.SW_Clrvw_Best_Visit_Times__c);
                    component.set("v.event.SW_Clrvw_RelatedCallId__c", pastEvent.SW_Clrvw_RelatedCallId__c);
                    component.set("v.event.SW_MyopiaStageOptions__c", pastEvent.SW_MyopiaStageOptions__c);

                    // Only set Phase values if they exist in past event
                    if (pastEvent.JJ_JPN_AST_Phase__c) {
                        component.set("v.ast_Phase", pastEvent.JJ_JPN_AST_Phase__c);
                        tempEvent.JJ_JPN_AST_Phase__c = pastEvent.JJ_JPN_AST_Phase__c;
                    }

                    if (pastEvent.JJ_JPN_MF_Phase__c) {
                        component.set("v.mf_Phase", pastEvent.JJ_JPN_MF_Phase__c);
                        tempEvent.JJ_JPN_MF_Phase__c = pastEvent.JJ_JPN_MF_Phase__c;
                    }
                    
                    /// Handle AST Recommend Target
                    if (pastEvent.JJ_JPN_AST_Recommend_Target__c) {
                        let astValues = pastEvent.JJ_JPN_AST_Recommend_Target__c.split(';');
                        component.set("v.astRecommendTarget", astValues);
                        tempEvent.JJ_JPN_AST_Recommend_Target__c = pastEvent.JJ_JPN_AST_Recommend_Target__c;
                    }

                    // Handle MF Recommend Target
                    if (pastEvent.JJ_JPN_MF_Recommend_Target__c) {
                        let mfValues = pastEvent.JJ_JPN_MF_Recommend_Target__c.split(';');
                        component.set("v.mfRecommendTarget", mfValues);
                        tempEvent.JJ_JPN_MF_Recommend_Target__c = pastEvent.JJ_JPN_MF_Recommend_Target__c;
                    }

                    // Set the updated event object once
                    component.set("v.event", tempEvent);

                } else {
                    
                }
            } else {
                
            }
        });
        $A.enqueueAction(action);
    },

    openModal : function(component,event,ui,helper) {
        component.set("v.event" ,{'sobjectType': 'Event'});
        component.set("v.event.StartDateTime", null);
        component.set("v.event.EndDateTime", null);
        component.set("v.event.IsRecurrence",false);
        component.find("everyMonth2").set("v.checked" , false);
        component.find("everyYear2").set("v.checked" , false);
        component.find("everySomeDay").set("v.checked",false);
        component.find("everyWeekDay").set("v.checked",true);
        component.set("v.event.RecurrenceMonthOfYear",1);
        document.getElementById("mon").checked  = false;
        document.getElementById("tue").checked  = false;
        document.getElementById("wed").checked  = false;
        document.getElementById("thu").checked  = false;
        document.getElementById("fri").checked  = false;
        document.getElementById("sat").checked  = false;
        document.getElementById("sun").checked  = false;
        document.getElementById("divDaily").style.display   = "block";
        document.getElementById("divWeekly").style.display  = "none";
        document.getElementById("divYearly").style.display  = "none";
        document.getElementById("divMonthly").style.display = "none";
        component.find("IsRecurrence").set("v.value" , false);
        document.getElementById("divhide").style.display = "none";
        let userId = $A.get("$SObjectType.CurrentUser.Id");
        let userName = $A.get("$SObjectType.CurrentUser.Name");
        component.set("v.selSubject","");
        if(document.getElementById("combobox-unique-id"))
            document.getElementById("combobox-unique-id").value ="";
        component.set("v.event.OwnerId",userId);
        component.set("v.startDateTimeVal", event.startDateTime);
        component.set("v.endDateTimeVal", event.endDateTime);
        component.set("v.value","Daily");
        component.set("v.selectedDOWValue" , $A.get("$Label.c.day"));
        component.set("v.selectedDOYValue" , $A.get("$Label.c.day"));
        component.set("v.event.RecurrenceEndDateOnly" , moment.utc(event.endDateTime).format("YYYY-MM-DD"));
        //<!-- Anil M_Start Need to comment-->
       /* let value = [{
            type: 'Event',
            id: ui.helper[0].dataset.sfid,
            label: "navigate",
        }];*/
        //<!-- Anil M_Start Need to comment-->
         
        //<!-- Anil M_Start Need to uncomment -->
        if(ui != null)
        {
            const var_26 = 26;
            let accString = ui.helper[0].dataset.sfname;
            accString = accString.length > var_26 ? accString.substring(0,var_26) + '..' : accString; 
            let accVar = {text:accString,val:ui.helper[0].dataset.sfid};
            component.set('v.selAccount',accVar);
            component.set("v.event.What.Name",accString);
        }
        
        //<!-- Anil M_End Need to uncomment-->
        
        let userValue = [{
            type: 'User',
            id: userId,
            label: userName,
        }];
        //<!-- Anil M_Start Need to comment-->
        //component.find("whatId").get("v.body")[0].set("v.values", value);
        //<!-- Anil M_End Need to comment-->
        component.find("ownerId").get("v.body")[0].set("v.values", userValue);
        
    },
    closeModal : function(component, event,helper) {
        helper.calDestroy(component, event, helper);
        let modal = component.find('modal');
        $A.util.removeClass(modal, 'slds-fade-in-open');
        let backdrop = component.find('backdrop');
        $A.util.removeClass(backdrop, 'slds-backdrop--open');
    },
    renderCal : function(component, event, helper,data) {
		$(document).ready(function(){
            $('#calendar').fullCalendar({
                header: {
                    center: 'today prev,next',
                    right: ''
                },
                locale: component.get("v.locale"),
                allDayText: 'All Day',
                defaultView: 'agendaWeek',//Added to make Week default
                defaultTimedEventDuration: '01:00:00',
                ignoretimezone: true,
				// Modified for AATB-4356 - JPN Lightning Time of Open Calendar
                minTime: ""+component.get("v.currentUser.SW_Start_of_Day__c")+":00:00",
                maxTime: ""+component.get("v.currentUser.SW_End_of_Day__c")+":00:00",
                // minTime: "08:00:00",
                // maxTime: "18:00:00",
				// Fix for 4686 date: April 3 2019  if default date is present then open default date else current date
                defaultDate: (component.get("v.defaultDate") === null) ? moment().format("YYYY-MM-DD") : component.get("v.defaultDate"),
                navLinks: true, // can click day/week names to navigate views
                listDayFormat : true,
                editable: true,
                droppable: true,
                eventLimit: true, // allow "more" link when too many events
                weekends: component.get("v.weekEnd"),
                eventBackgroundColor: 'rgb(109, 146, 179)',
                eventTextColor: 'white',
                // Date Mar 4 2019 Project:skywalker fix for AATB-4358
                weekNumberCalculation:component.get('v.currentUser.LocaleSidKey')=='ja_JP'?"ISO":"",
                events: data,
                eventRender: function(event, element) {
                    if(typeof event.rec !== "undefined")
                        element.find(".fc-content").prepend("<img src='/img/recurring_activity.gif' style='filter: contrast(100%) brightness(50%);width: 0.8rem;' alt='()'/>");
                	$(element).css("backgroundColor", event['eventBackgroundColor']); // Added for AATB-6519
                },
                eventClick: function(calEvent, jsEvent, view) {
                    // AATB-4681 - Store current values to regain on redirection post event deletion
                    sessionStorage.setItem('evtStDate',calEvent.start.format('YYYY-MM-DD'));
                    sessionStorage.setItem('weekend',component.get("v.weekEnd"));
                    component.set('v.idVal', calEvent.id);
                    if(component.get('v.usertype') != $A.get("$Label.c.SW_Standard"))	
                    {		
                        window.open($A.get("$Label.c.SW_DealerCommunityURL")+'/s/detail/'+calEvent.id,'_top');	
                    }	
                    else{	
                        window.open('/lightning/r/Event/'+calEvent.id+'/view','_top');	
                    }
                },
                eventDataTransform: function(event) {
                    let evt;
                    // Salesforce Event
                    if (event.Id) {
                        evt = this.sObjectToEvent(event);
                    }
                    // Regular Event
                    else {
                        evt = event;
                    }
                    return evt;
                },
                eventDrop: function(event, delta, revertFunc) {
                    //Anil M - Start Uncomment
                    component.set("v.IsSpinner",true);
                    //Anil M -End Uncomment
                    let stDate = moment.utc(event.start._d).add(-(component.get('v.offsetVal')), 'hours');
                    let endDate = moment.utc(event.end._d).add(-(component.get('v.offsetVal')), 'hours');
                    let evObj = {
                        "Id" : event.id,
                        "StartDateTime" : stDate.format(),
                        "EndDateTime" : endDate.format()
                    };
                    helper.upsertEvent(component, event ,helper, evObj);
                },
                drop: function(event,jsEvent,ui,resourceId) {
                    let stDate = moment.utc(event._d).add(-(component.get('v.offsetVal')), 'hours');
                    let endDate = moment.utc(event._d).add(-(component.get('v.offsetVal')), 'hours');
                    let evObj = {
                        "Id" : event.id,
                        "title" : event.title,
                        "startDateTime" : stDate.format(),
                        "endDateTime" : endDate.add(1, 'hours').format(),
                        "description" : event.description
                    };
                    component.set("v.NewEvt", true);
                    helper.openModal(component,evObj,ui,helper);
                }
            });
            component.set("v.IsSpinner",false); // Included for AATB-6519 to show spinner till events are fetched
            // AATB-4681 - Clear session storage once rendering calendar post event deletion
            sessionStorage.setItem('evtStDate',moment().format("YYYY-MM-DD"));
            sessionStorage.setItem('weekend',null);
        });
    },
    initComponent : function(component, event, helper) {
        let action = component.get('c.initComponent');
        action.setParams({
            objectType: component.get('v.object'),
            fields: component.get('v.fields').split(',')
        });
        action.setCallback(this, function(response) {
            let state = response.getState();
            if(state == 'SUCCESS') {
                let fieldsList = JSON.parse(response.getReturnValue());
                component.set('v.fieldsList', fieldsList);
            }
        });
        $A.enqueueAction(action);
    },
    // Included for AATB-4356 - JPN Lightning Time of Open Calendar
	// Changed for AATB-6573 to get fields for Hospital list view
	fetchCurrentUser : function(component, event, helper, defaultListView) {
        let selectedView = (defaultListView == null) ? component.find("selectedViewId").get("v.value") : defaultListView;
        let action = component.get('c.fetchUserDetails');
        action.setCallback(this, function(response) {
            let state = response.getState();
            if(state == 'SUCCESS') {
                let currentUser = response.getReturnValue();
                component.set('v.currentUser', currentUser);
                /* Implemented for AATB-5188 - To remove Account Phone from account list views for JPN*/
                let actionLocale = component.get('c.fetchUserLocaleSettings');
                actionLocale.setParams({
                    localeSidKey: component.get('v.currentUser.LocaleSidKey'),
                    listViewId: selectedView
                });
                actionLocale.setCallback(this, function(response) {
                    let state = response.getState();
                    if(state == 'SUCCESS') {
                        let resp = response.getReturnValue();
                        if(!$A.util.isUndefined(resp) && !$A.util.isEmpty(resp)) {
                            let fields = response.getReturnValue();
                            component.set('v.fields',fields);
                            helper.initComponent(component, event,helper);
                        }
                    }
                });
                $A.enqueueAction(actionLocale);
                // Included for AATB-4357 - JPN Lightning Uncheck checkbox in Open Calendar
                // AATB-4681 - Modified the logic of setting checkbox value wrt session settings
                if(sessionStorage.getItem('weekend')!='undefined' && sessionStorage.getItem('weekend')!=null){
                    let weekend = JSON.parse(sessionStorage.getItem('weekend'));
                    component.set('v.weekEnd', ((weekend === null) ? ((component.get('v.currentUser.LocaleSidKey') === 'ja_JP') ? false : true) : weekend));
                }
            }
        });
        $A.enqueueAction(action);
    },
    fetchRecords : function(component, event,helper, defaultListView) {
        component.set("v.IsSpinner",true);
        let fieldsList = component.get('v.fieldsList');
        let fields = [];
        for(let iIndex = 0; iIndex < fieldsList.length; iIndex++) {
            fields.push(fieldsList[iIndex].name.trim());
        }
        let selected;
        if(defaultListView == null){
            selected = component.find("selectedViewId").get("v.value");
        }else{
            selected = defaultListView;
        }
        let action = component.get('c.fetchRecords');
        action.setParams({
            objectType: component.get('v.object'),
            fields: fields,
            keyword: component.get('v.keyword'),
            sortField : component.get('v.sortField'),
            sortDir : component.get('v.sortDir'),
            iLimit: component.get('v.limit'),
            filterId: selected
        });
        let spinner = component.find('Spinner');
        $A.util.toggleClass(spinner, 'slds-hide');
        action.setCallback(this, function(response) {
            $A.util.toggleClass(spinner, 'slds-hide');
            let state = response.getState();
            if(state == 'SUCCESS') {
                let unsortedAccountValue = response.getReturnValue().accList;
                let records = response.getReturnValue().accList;
                component.set('v.unsortedAccountValue', unsortedAccountValue);
                let pageSize = component.get('v.pageSize');
                let pageNumber = component.get('v.pageNumber');
                let pages = this.chunkify(records, pageSize);
                component.set('v.offsetVal', response.getReturnValue().offset);
                component.set('v.pages', pages);
                component.set('v.records', pages[pageNumber]);
				component.set('v.usertype', response.getReturnValue().usertype);
                setTimeout(function() {
                    $('.dummy').each(function() {
                        // store data so the calendar knows to render an event upon drop
                        $(this).data('event', {
                            title: $.trim($(this).text()), // use the element's text as the event title
                            stick: true // maintain when user navigates (see docs on the renderEvent method)
                        });
                        // make the event draggable using jQuery UI
                        $(this).draggable({
                            containment: 'document',
                            // return a custom styled elemnt being dragged
                            helper: function (event) {
                                //Anil M - Start Comment
                                //return $('<div class="uv-planning-dragging" data-sfid="'+ $(this)[0].dataset.sfid +'"></div>').html($(this).text());
                                //Anil M - End Comment
                                
                                //Anil M - Start Uncomment
                                return $('<div class="uv-planning-dragging " data-sfname="'+$(this)[0].dataset.sfname+'" data-sfid="'+ $(this)[0].dataset.sfid +'"></div>').html($(this).text());
                                //Anil M - End Uncomment
                            },
                            opacity: 0.70,
                            zIndex: 10000,
                            appendTo: 'body',
                            cursor: 'move',
                            revertDuration: 0,
                            revert: true
                        });
                    });
                    component.set("v.IsSpinner",false);
                }, 3000);
            }
            // AATB-5489 - Handle error scenario while fetching records with pinned list view which is unshared now
            else if(state === 'ERROR') {
                let selectedListView = component.get("v.AccountListViewList")[0].id;
                helper.fetchRecords(component,event,helper,selectedListView);
            }
        })
        //Fix for 4602 date: march 6 2019
        helper.pinningAndUnpinning(component,event,helper);
        $A.enqueueAction(action);
    },
    chunkify : function(data, size){
        let sets = [], chunks, i = 0;
        chunks = data.length / size;
        while(i < chunks){
            sets[i] = data.splice(0, size);
            i++;
        }
        return sets;
    },
    //----LISTVIEW---//
    getAccountHelper : function(component,event,defaultListView) {
        let selected;
        if(defaultListView == null){
            selected = component.find("selectedViewId").get("v.value");
        }else{
            selected = defaultListView;
        }
        let action = component.get("c.getFilteredAccounts");
        action.setParams({filterId : selected});
        action.setCallback(this, function(response){
            let state = response.getState();
            if (state === "SUCCESS") {
                component.set("v.records",response.getReturnValue());
            }
        });
        $A.enqueueAction(action);
    },
    tranformToFullCalendarFormat : function(component,event,helper) {
        let eventArr = [];
        let whatName = '';
        let eventsRcvd = event;
        let allDay = false;
		let subject = '';
		
		for(let i = 0;i < eventsRcvd.length;i++){
			whatName = ''; // AATB-4643 - Assign empty string for each event
            subject = ''; // AGFB-18009 - Fix empty subject issue.
            //AATB-12518
            if (eventsRcvd[i].Subject) {
                subject = eventsRcvd[i].Subject;
                //AATB-12518
                if(eventsRcvd[i].Subject.indexOf("_")!=-1){
                    subject=  eventsRcvd[i].Subject.substring(0,eventsRcvd[i].Subject.indexOf("_"));
            	}
             
            } else {
                // Handle the case where Subject is undefined or null
                subject = 'No Subject';
            }
			allDay = false;
            if(eventsRcvd[i].Offset === 0)
                allDay = true;
            if(typeof eventsRcvd[i].WhatId !== "undefined")
                whatName = eventsRcvd[i].WhatName + ' : ';
            
            eventArr.push({
                'id':eventsRcvd[i].Id,
                'start':moment(moment(eventsRcvd[i].StartDateTime)._d).add(eventsRcvd[i].Offset,'hours').toISOString(),
                'end':  moment(moment(eventsRcvd[i].EndDateTime)._d).add(eventsRcvd[i].Offset, 'hours').toISOString(),
                'title':whatName +subject,
                'What':eventsRcvd[i].WhatId,
                'rec':eventsRcvd[i].Recurrence2PatternText != null ? eventsRcvd[i].Recurrence2PatternText : eventsRcvd[i].RecurrenceActivityId ,
                'allDay': allDay,
                'eventBackgroundColor': eventsRcvd[i].BGColor // Added for AATB-6519
            });
        }
		
        helper.renderCal(component,event,helper,eventArr);
        component.set("v.events",eventArr);
    },
    fetchEvents : function(component,event,helper) {
        component.set("v.IsSpinner",true); // Added for AATB-6519
        let action = component.get("c.getEventsPallete");
        action.setCallback(this, function(response) {
            let state = response.getState();
            if(state === "SUCCESS"){
                helper.tranformToFullCalendarFormat(component,response.getReturnValue(),helper);
            }
        });
        $A.enqueueAction(action);
    },
    helperOnchange:function(component,event,helper){
        let checkBox = document.getElementById("weekend");
        if (checkBox.checked){
            component.set("v.weekEnd" ,true);
            helper.calDestroy(component,event,helper);
        }
        else{
            component.set("v.weekEnd" ,false);
            helper.calDestroy(component,event,helper);
        }
    },
    helperSaveEventRecords:function(component,event,helper){
        //<!-- Anil M_Start Need to uncomment-->
          component.set("v.IsSpinner",true);
        //<!-- Anil M_End Need to uncomment-->
        if(component.find("IsRecurrence").get("v.value"))
		{
         helper.setRecurrencePattern(component,event,helper);
		}
        let eventInfo = {};
        let selectedRecordTypeName = component.get("v.recordTypeName");
        if(selectedRecordTypeName !== "Telesales_JPN" && selectedRecordTypeName !== "Telesupport_Call_JPN"
            && ($A.util.isEmpty(component.find("ownerId").get("v.value")) ||
               $A.util.isEmpty(component.find("startTime").get("v.value")) ||
               $A.util.isEmpty(component.find("endTime").get("v.value")))){
                let toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    title : 'Error Message',
                    message:'Please Enter the required fields',
                    messageTemplate: 'Mode is pester ,duration is 1sec and Message is overrriden',
                    duration:' 1000',
                    key: 'info_alt',
                    type: 'error',
                    mode: 'pester'
                });
                toastEvent.fire();
               //<!-- Anil M_Start Need to uncomment-->
                component.set("v.IsSpinner",false); 
               //<!-- Anil M_End Need to uncomment-->
                return false;
            }
        if(component.find("IsRecurrence").get("v.value") && (component.get("v.event.RecurrenceEndDateOnly") == null || component.get("v.event.Recurrence2PatternText").includes("undefined") || component.get("v.event.Recurrence2PatternText").includes("null"))){
            let toastEvent = $A.get("e.force:showToast");
            toastEvent.setParams({
                title : 'Error Message',
                message:'Please Enter the Recurrence Values',
                messageTemplate: 'Mode is pester ,duration is 1sec and Message is overrriden',
                duration:' 1000',
                key: 'info_alt',
                type: 'error',
                mode: 'pester'
            });
            toastEvent.fire();
            return false;
        }
        else{
            eventInfo = component.get("v.event");
            delete eventInfo.What;
            delete eventInfo.Owner;
            delete eventInfo.Who;
            //Clear recurrence fields
            delete eventInfo.GroupEventType;
            delete eventInfo.RecurrenceStartDateTime;
            delete eventInfo.RecurrenceDayOfMonth;
            delete eventInfo.RecurrenceDayOfWeekMask;
            delete eventInfo.RecurrenceEndDateOnly;  
            delete eventInfo.RecurrenceInstance;
            delete eventInfo.RecurrenceInterval;
            delete eventInfo.RecurrenceMonthOfYear;
            delete eventInfo.RecurrenceTimeZoneSidKey;
            delete eventInfo.RecurrenceType;
            delete eventInfo.IsRecurrence;
            eventInfo.ActivityDateTime = eventInfo.StartDateTime;
            let rec= component.get("v.recTypeID");
            eventInfo.RecordTypeId = rec;
            eventInfo.StartDateTime  = component.find("startTime").get("v.value");
            eventInfo.EndDateTime  = component.find("endTime").get("v.value");
            
            let action = component.get("c.saveEvent");
            if(component.get("v.selSubject") == '' || component.get("v.selSubject") == null){
                eventInfo.Subject = document.getElementById("combobox-unique-id").value;
            }else{
                eventInfo.Subject = component.get("v.selSubject")["val"];
            }
            if(eventInfo.Subject == ''){
                let toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    title : 'Error Message',
                    message:'Please Enter the required fields',
                    messageTemplate: 'Mode is pester ,duration is 1sec and Message is overrriden',
                    duration:' 1000',
                    key: 'info_alt',
                    type: 'error',
                    mode: 'pester'
                });
                toastEvent.fire();
                //<!-- Anil M_Start Need to uncomment-->
                 component.set("v.IsSpinner",false); 
                //<!-- Anil M_End Need to uncomment-->
                return false;
            }
            //AATB-4607 April 3 2019  save eventwhoids
            let nameArr = [];
            if(component.find("whoId").get("v.value") != undefined  && component.find("whoId").get("v.value") != "[]"){
                //AATB-4607 April 3 2019  save eventwhoids
                let nameValues;
                //AATB-4961 April 9 2019  save who id in component becuase it is getting lost when clicking on save twice
                if(helper.isJson(component.find("whoId").get("v.value"))){
                    component.set("v.whoid",component.find("whoId").get("v.value"));
                    nameValues = JSON.parse(component.find("whoId").get("v.value"));
                }
                else{
                    nameValues = JSON.parse(component.get("v.whoid"));
                }
                for (let i=1; i<nameValues.length; i++) {
                    nameArr.push(nameValues[i].id);
                }
                eventInfo.WhoId = nameValues[0].id;
            }
            if(component.find("whoId").get("v.value") == "[]"){
                eventInfo.WhoId = '';
            }
            // Save with value if field not available in layout
            for(let i in component.get("v.fieldAPIList")){
                if(component.get("v.fieldAPIList")[i] !== 'Subject') {
                    let eachFieldApi = component.get("v.eventRTPickList."+component.get("v.fieldAPIList")[i]);
                    for (let j=0; j<eachFieldApi.length; j++){
                        if(component.get("v.event."+component.get("v.fieldAPIList")[i]) === eachFieldApi[j].label){
                            component.set("v.event."+component.get("v.fieldAPIList")[i],eachFieldApi[j].value);
                        }
                    }
                }
            }
            //AATB-4607 April 3 2019  save eventwhoids
            action.setParams({ "eventInfo" : JSON.stringify(eventInfo),
                              "relationIds" : nameArr});
            action.setCallback(this, function(response) {
                let state = response.getState();
                if (state === "SUCCESS"){
                    if(response.getReturnValue().status === "SUCCESS" && response.getReturnValue().msg == 'Lightning Recurrence')
                    {
                        let toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Success!",
                            "type":"Success",
                            "message": "Creation of recurring events take time, you will be notified through an email."
                        });
                        toastEvent.fire();
                        let modal = component.find('modal');
                        $A.util.removeClass(modal, 'slds-fade-in-open');
                        let backdrop = component.find('backdrop');
                        $A.util.removeClass(backdrop, 'slds-backdrop--open');
                        helper.calDestroy(component, event, helper);  
                    }
                    else if(response.getReturnValue().status === "SUCCESS"){
                        let toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Success!",
                            "type":"Success",
                            "message": "Event added successfully."
                        });
                        toastEvent.fire();
                        let modal = component.find('modal');
                        $A.util.removeClass(modal, 'slds-fade-in-open');
                        let backdrop = component.find('backdrop');
                        $A.util.removeClass(backdrop, 'slds-backdrop--open');
                        helper.calDestroy(component, event, helper);
                    }
                    else{
                        let toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Error!",
                            "type":"error",
                            "message": response.getReturnValue().msg
                        });
                        toastEvent.fire();
						// Below line is for setting RecurrenceEndDateOnly field after it gets deleted in above logic (Before we call apex method)
						// If do no set then logic to set Recurrence2PatternText may fail
                        component.set("v.event.RecurrenceEndDateOnly",moment.utc(component.find("startTime").get("v.value")).format("YYYY-MM-DD"));
                    }
                    //<!-- Anil M_Start Need to uncomment-->
                     component.set("v.IsSpinner",false); 
                    //<!-- Anil M_End Need to uncomment-->
                }
                else if(state === "ERROR"){
                    let errors = action.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            let toastEvent = $A.get("e.force:showToast");
                            toastEvent.setParams({
                                title : 'Error Message',
                                message:errors[0].message,
                                messageTemplate: 'Mode is pester ,duration is 5sec and Message is overrriden',
                                duration:' 5000',
                                key: 'info_alt',
                                type: 'error',
                                mode: 'pester'
                            });
                            toastEvent.fire();
							// Below line is for setting RecurrenceEndDateOnly field after it gets deleted in above logic (Before we call apex method)
							// If do no set then logic to set Recurrence2PatternText may fail
                            component.set("v.event.RecurrenceEndDateOnly",moment.utc(component.find("startTime").get("v.value")).format("YYYY-MM-DD"));
                        }
                    }
                   //<!-- Anil M_Start Need to uncomment-->
                    component.set("v.IsSpinner",false); 
                   //<!-- Anil M_End Need to uncomment--> 
                }
            });
            $A.enqueueAction(action);
        }
    },
    setDraggableUI : function(component, event, helper){
        setTimeout(function() {
            $('.dummy').each(function() {
                // store data so the calendar knows to render an event upon drop
                $(this).data('event', {
                    title: $.trim($(this).text()), // use the element's text as the event title
                    stick: true // maintain when user navigates (see docs on the renderEvent method)
                });
                // make the event draggable using jQuery UI
                $(this).draggable({
                    zIndex: 999,
                    revert: true,      // will cause the event to go back to its
                    revertDuration: 0  //  original position after the drag
                });
            });
        }, 3000);
    },
    upsertEvent : function(component, event, helper, evObj) {
        let action = component.get("c.upsertEvents");
        action.setParams({"sEventObj": evObj});
        action.setCallback(this, function(response) {
            //<!-- Anil M_Start Need to uncomment-->
             component.set("v.IsSpinner",false);
            //<!-- Anil M_End Need to uncomment-->
            let state = response.getState();
            if (state === "SUCCESS"){
                if(response.getReturnValue() === "success"){
                    let toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Success!",
                        "type":"Success",
                        "message": " Event shifted successfully. "
                    });
                    toastEvent.fire();
                }
                else{
                    let toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "type":"error",
                        "message": response.getReturnValue()
                    });
                    toastEvent.fire();
                    helper.calDestroy(component, event, helper);
                }
            }
            else{
                let toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Error!",
                    "type":"error",
                    "message": response.getReturnValue()
                });
                toastEvent.fire();
                helper.calDestroy(component, event, helper);
            }
        });
        $A.enqueueAction(action);
    },
    helperOncheck:function(component,event,helper){
        if(component.find("IsRecurrence").get("v.value") === true){
            document.getElementById("divhide").style.display = "block";
        }
        else{
            document.getElementById("divhide").style.display = "none";
            component.set("v.event.IsRecurrence",component.find("IsRecurrence").get("v.value"));
        }
    },
    helperOnFreqchange:function(component,event,helper){
        if(component.get("v.value") === "Weekly"){
            document.getElementById("divWeekly").style.display = "block";
            document.getElementById("divMonthly").style.display = "none";
            document.getElementById("divYearly").style.display = "none";
            document.getElementById("divDaily").style.display = "none";
            document.getElementById("mon").checked  = false;
            document.getElementById("tue").checked  = false;
            document.getElementById("wed").checked  = false;
            document.getElementById("thu").checked  = false;
            document.getElementById("fri").checked  = false;
            document.getElementById("sat").checked  = false;
            document.getElementById("sun").checked  = false;
        }
        else if(component.get("v.value") === "Monthly"){
            document.getElementById("divWeekly").style.display = "none";
            document.getElementById("divMonthly").style.display = "block";
            document.getElementById("divYearly").style.display = "none";
            document.getElementById("divDaily").style.display = "none";
            component.find("everyMonth1").set("v.checked" , true);
            component.find("everyMonth2").set("v.checked" , false);
        }
            else if(component.get("v.value") === "Yearly"){
                document.getElementById("divWeekly").style.display = "none";
                document.getElementById("divMonthly").style.display = "none";
                document.getElementById("divYearly").style.display = "block";
                document.getElementById("divDaily").style.display = "none";
                component.find("everyYear1").set("v.checked",true);
                component.find("everyYear2").set("v.checked" , false);
            }
                else{
                    document.getElementById("divWeekly").style.display = "none";
                    document.getElementById("divMonthly").style.display = "none";
                    document.getElementById("divYearly").style.display = "none";
                    document.getElementById("divDaily").style.display = "block";
                    component.find("everySomeDay").set("v.checked",false);
                    component.find("everyWeekDay").set("v.checked",true);
                }
    },
    helperonDailyChange:function(component,event,helper){
        if(event.getSource().getLocalId() === "everyWeekDay"){
            component.find("everySomeDay").set("v.checked" , false);
        }
        else if(event.getSource().getLocalId() === "everySomeDay"){
            component.find("everyWeekDay").set("v.checked" , false);
        }
    },
    helperonMonthChange:function(component,event,helper){
        if(event.getSource().getLocalId() === "everyMonth1"){
            component.find("everyMonth2").set("v.checked" , false);
        }
        else if(event.getSource().getLocalId() === "everyMonth2"){
            component.find("everyMonth1").set("v.checked" , false);
        }
    },
    helperonYearChange:function(component,event,helper){
        if(event.getSource().getLocalId() === "everyYear1"){
            component.find("everyYear2").set("v.checked" , false);
        }
        else if(event.getSource().getLocalId() === "everyYear2"){
            component.find("everyYear1").set("v.checked" , false);
        }
    },
    helperonDOWChange:function(component,event,helper){
        component.set("v.selectedDOWValue" , component.find('RecurrenceDayOfWeekMaskID').get('v.value'));
    },
    helperonDOYChange:function(component,event,helper){
        component.set("v.selectedDOYValue" , component.find('RecurrenceDayOfYearMaskID').get('v.value'));
    },
    fetchSearchedRecords : function(component,event,helper){
        component.set("v.IsSpinner",true);
        let data = component.get("v.unsortedAccountValue");
        let searchKey = component.get('v.keyword');
        let searchedData =[];
        //AATB-4836 bug fix Date 3/19/2018 fix for searchdata missing on clearing and page hanging while searching
        searchedData = data.filter(o => (o.Name.toLowerCase().includes(searchKey.toLowerCase()))||
                                   (typeof o.Phone !== "undefined" &&  o.Phone.includes(searchKey)) ||
                                   (typeof o.OutletNumber__c!== "undefined" && o.OutletNumber__c.includes(searchKey)));
        let pageSize = component.get('v.pageSize');
        // Fix pagination issue
        const pageZero = 0;
        let pageNumber = pageZero;
        let pages;
        pages = this.chunkify(searchedData, pageSize);
        component.set('v.pages', pages);
        component.set('v.records', pages[pageNumber]);
        component.set('v.pageNumber', pageZero); // Fix pagination issue
        setTimeout(function() {
            $('.dummy').each(function() {
                // store data so the calendar knows to render an event upon drop
                $(this).data('event', {
                    title: $.trim($(this).text()), // use the element's text as the event title
                    stick: true // maintain when user navigates (see docs on the renderEvent method)
                });
                // make the event draggable using jQuery UI
                $(this).draggable({
                    containment: 'document',
                    // return a custom styled elemnt being dragged
                    helper: function (event) {
                        //<!-- Anil M_Start Need to comment-->
                       // return $('<div class="uv-planning-dragging" data-sfid="'+ $(this)[0].dataset.sfid +'"></div>').html($(this).text());
                        //<!-- Anil M_Start Need to comment-->
                        
                        //<!-- Anil M_Start Need to uncomment-->
                        return $('<div class="uv-planning-dragging" data-sfname="'+$(this)[0].dataset.sfname+'" data-sfid="'+ $(this)[0].dataset.sfid +'"></div>').html($(this).text());                        
                        //<!-- Anil M_End Need to uncomment-->
                    },
                    opacity: 0.70,
                    zIndex: 10000,
                    appendTo: 'body',
                    cursor: 'move',
                    revertDuration: 0,
                    revert: true
                });
            });
            component.set("v.IsSpinner",false);
        }, 3000);
    },
    //Fix for 4602 date: march 6 2019 this function is called when user clicks on pin icon to pin listview
    helperPinAccount:function(component,event,helper){
		let action = component.get('c.fetchUserPinnedListviews');
        let selectedListView=component.find("selectedViewId").get("v.value");
        if(!selectedListView){
            selectedListView=(component.get("v.AccountListViewList")[0]).id;
        }
        let pinListViewWrapperr= {objectName: 'Account', listView :  selectedListView};
        action.setParams({
			"wrap":pinListViewWrapperr
        });
        action.setCallback(this, function(response) {
            let state = response.getState();
			let value=response.getReturnValue();
            if (state === "SUCCESS"){
                component.set("v.listviewSelected",value.SW_ListViewId__c);
                helper.pinningAndUnpinning(component,event,helper,'helperPinAccount');
            }
        })
        $A.enqueueAction(action);
    },
    // Fix for 4602 date: march 6 2019 this function is called in the init to check if user already has a Pinned listview or not
    fetchUserDetails:function(component,event,helper){
		let action = component.get('c.fetchUserPinnedListviews');
        let pinListViewWrapperr= {objectName: 'Account'};
        action.setParams({
			"wrap":pinListViewWrapperr
        });
        action.setCallback(this, function(response) {
            let state = response.getState();
            let value=response.getReturnValue();
            if (state === "SUCCESS"){
                if(value){
                    component.set("v.listviewSelected",value.SW_ListViewId__c);
                }
            }
        })
        $A.enqueueAction(action);
    },
    // Fix for 4602 date: march 6 2019 this is to change the color of pin when pinned and unpinned
    pinningAndUnpinning:function(component,event,helper,firedfrom){
        let sel= component.get("v.listviewSelected");
        let pin=  component.find("pin");
        //if previously pin is unpinned and it is pinned now then only show the toast message
        let pinstatePrev= pin.get("v.iconName");
        if(sel==component.find("selectedViewId").get("v.value")){
            pin.set("v.iconName","utility:pinned");
            $A.util.addClass(pin,'fill');
            //dont show the toast message if it is fired from other methods other than helperPinAccount
            if(pinstatePrev=='utility:pin'&& (firedfrom=='helperPinAccount')){
                let allListViews=component.get("v.AccountListViewList");
                let pinnedListView ;
                //AATB-4602 adding (Pinned list) to label manually and removing after some other thing is pinned
                for (let i = 0; i < allListViews.length; i++) {
                    if(allListViews[i].id==sel){
                        console.log(allListViews[i].label);
                        pinnedListView=allListViews[i].label;
                        allListViews[i].label =   allListViews[i].label+' (Pinned list)';
                    }else{
                        if(allListViews[i].label.indexOf('Pinned list')!=-1){
                            allListViews[i].label=allListViews[i].label.substring(0,allListViews[i].label.indexOf('Pinned list')-2);
                        }
                    }
                }
                component.set("v.AccountListViewList",allListViews);
                //this is done becuase sometimes the object change is not getting detected its a lightning aura
                //change handler issue
                component.set("v.AccountListViewList",allListViews[0]);
                component.set("v.AccountListViewList",allListViews);                       
                component.find("selectedViewId").set("v.value",'');
                component.find("selectedViewId").set("v.value",sel);
                let toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    message:pinnedListView+' Listview is Pinned',
                    messageTemplate: 'Mode is pester ,duration is 1sec and Message is overrriden',
                    duration:' 500',
                    key: 'info_alt',
                    type: 'success',
                    mode: 'pester'
                });
                toastEvent.fire();
            }
        }else{
            pin.set("v.iconName","utility:pin");
            $A.util.removeClass(pin,'fill');
        }
    },
    isJson: function(str) {
        try{
            JSON.parse(str);
        }catch(e){
            return false;
        }
        return true;
    },
    updateAccount:function(component,event,helper){
        if(component.get("v.selAccount") != null && component.get("v.selAccount") != '')
        {   const var_28 = 28;
            const var_26 = 26;
            component.set("v.event.What.Name",component.get("v.selAccount")["text"]);
         	component.find("whatId").set("v.value",component.get("v.selAccount")["val"]);
         	if(component.get("v.selAccount")["text"].length > var_28)
            {
                let accStr = component.get("v.selAccount")["text"];
                accStr = accStr.length > var_26 ? accStr.substring(0,var_26) + '..' : accStr;
                let accName = {text:accStr,val:component.get("v.selAccount")["val"]};
                component.set('v.selAccount',accName);
            }
            component.set('v.event.WhatId',component.get("v.selAccount")["val"]);
        }
        else
        {
            component.set('v.event.WhatId','');
            component.set("v.event.What.Name",'');
            // Clear phase values when account is cleared
            this.clearPhaseValues(component);
        }
        this.updateSubject(component,event,helper);
    },
    updateSubject:function(component,event,helper){
        if(JSON.stringify(component.get('v.eventSubjectmdt')).includes(component.get("v.recordTypeName"))){
            let subject;
			const var_18 = 18;
            if (typeof component.get("v.event.What.Name") != 'undefined' && component.get("v.event.What.Name") != '') {
                let subjectStr = component.get("v.subjectDefaultVal") + '_' + component.get("v.event.What.Name");
                component.set("v.FullSubject",subjectStr);
                let subjectNewStr = subjectStr.length > var_18 ? subjectStr.substring(0,var_18) + '..' : subjectStr;
                subject = {text:subjectNewStr,val:subjectStr};
            } else {
                subject = {text:component.get("v.subjectDefaultVal"),val:component.get("v.subjectDefaultVal")};//updated for AATB-5370
                component.set("v.FullSubject",component.get("v.subjectDefaultVal"));
            }
            component.set("v.selSubject",'');
            component.set("v.selSubject",subject);
      }
    },
    // Added for AATB-5364 to update EndDateTime when StartDateTime is changed
    updateEndTimeHelper:function(component,event,helper){
        let startDateOld = moment(event.getParam("oldValue"));
        let startDateNew = moment(event.getParam("value"));
        let endDateOld = moment(component.get('v.endDateTimeVal'));
        let diffTime = endDateOld.diff(startDateOld, 'minutes');
        let endDateNew = startDateNew.add(diffTime, 'minutes');
        component.set('v.endDateTimeVal',endDateNew.format());
    },
    //Added for AATB-5370 to fetch the record types for dynamic subject population
    fetchRecordTypes :function(component,event,helper){
        let action = component.get('c.fetchSubjectRecordTypes');
        action.setCallback(this, function(response){
            let state = response.getState();
            if (state === "SUCCESS") {
                let result = response.getReturnValue();
                component.set('v.eventSubjectmdt',result);
            }
        });
        $A.enqueueAction(action);
    },
    //Added to fix AATB-6172 to check the accessibilty for Call Objective #2 field
    checkIsAccessible:function(component,event,helper){
        let action = component.get("c.isAccessible");
        action.setParams({
            sObjectType : 'Event',
            fieldName : 'CallObjective2__c'
        });
        action.setCallback(this, function(response){
            let state = response.getState();
            if (state === "SUCCESS") {
				component.set("v.isCallObjective2Accesible",response.getReturnValue());
            }
        });
        $A.enqueueAction(action);
    },
    setRecurrencePattern:function(component,event,helper)
    {	let special = {'First':'1', 'Second':'2','Third':'3','Fourth':'4','Last':'31'}; 
        component.set("v.RecurrencePatternText",'');
        if(component.find("freqId").get("v.value") == 'Daily')
        {
         helper.setDailyRecurr(component,event);
        }
        else if(component.find("freqId").get("v.value") == 'Weekly')
        {
         helper.setWeeklyRecurr(component,event);
        }
        else if(component.find("freqId").get("v.value") == 'Monthly')
        {
         helper.setMonthlyRecurr(component,special);   
        }
        else if(component.find("freqId").get("v.value") == 'Yearly')
        {
         helper.setYearlyRecurr(component,special);     
        }
        component.set("v.event.Recurrence2PatternText",component.get("v.RecurrencePatternText"));
    },
    setDailyRecurr:function(component)
    {
        if(component.find("everyWeekDay").get("v.checked"))  
	    {
        component.set("v.RecurrencePatternText",("RRULE:FREQ=DAILY"+";UNTIL="+component.get("v.event.RecurrenceEndDateOnly").replaceAll('-', '')+'T235900Z'));
        }
		if(component.find("everySomeDay").get("v.checked"))
        {
		component.set("v.RecurrencePatternText",("RRULE:FREQ=DAILY"+";INTERVAL="+component.get("v.event.RecurrenceInterval")+";UNTIL="+component.get("v.event.RecurrenceEndDateOnly").replaceAll('-', '')+'T235900Z'));
		}
	},
    setWeeklyRecurr:function(component)
    {
		component.set("v.RecurrencePatternText",("RRULE:FREQ=WEEKLY"+";INTERVAL="+component.get("v.event.RecurrenceInterval")+";UNTIL="+component.get("v.event.RecurrenceEndDateOnly").replaceAll('-', '')+'T235900Z'));
        let weeks = ';BYDAY=';
        if(document.getElementById("mon").checked)
        {
		weeks +=  'MO,';
        }
		if(document.getElementById("tue").checked)
        {
		weeks +=  'TU,';
        }
		if(document.getElementById("wed").checked)
        {
		weeks +=  'WE,';
        }
		if(document.getElementById("thu").checked)
        {
		weeks +=  'TH,';
        }
		if(document.getElementById("fri").checked)
        {
		weeks +=  'FR,';
        }
		if(document.getElementById("sat").checked)
        {
		weeks +=  'SA,';
		}
        if(document.getElementById("sun").checked)
        {
		weeks +=  'SU,';
        }
        weeks = weeks.substring(0, weeks.length - 1);
        component.set("v.RecurrencePatternText",(component.get("v.RecurrencePatternText")+weeks));
    },
    setMonthlyRecurr:function(component,special)
    {
		const var_2 = 2;
        component.set("v.RecurrencePatternText",("RRULE:FREQ=MONTHLY"+";INTERVAL="+component.get("v.event.RecurrenceInterval")+";UNTIL="+component.get("v.event.RecurrenceEndDateOnly").replaceAll('-', '')+'T235900Z'));
		if(component.find("everyMonth1").get("v.checked"))
        {
            component.set("v.RecurrencePatternText",(component.get("v.RecurrencePatternText")+";BYMONTHDAY="+component.get("v.event.RecurrenceDayOfMonth")));   
        }
        else if(component.find("everyMonth2").get("v.checked") && component.get("v.selectedDOWValue") != $A.get("$Label.c.day") && component.get("v.event.RecurrenceInstance") != 'Last')   
        {
            component.set("v.RecurrencePatternText",(component.get("v.RecurrencePatternText")+";BYDAY="+special[component.get("v.event.RecurrenceInstance")]+component.get("v.selectedDOWValue").toUpperCase().substring(0,var_2)));  
        }
        else if(component.find("everyMonth2").get("v.checked") && component.get("v.selectedDOWValue") != $A.get("$Label.c.day") && component.get("v.event.RecurrenceInstance") == 'Last')
        {
            component.set("v.RecurrencePatternText",(component.get("v.RecurrencePatternText")+";BYDAY="+component.get("v.selectedDOWValue").toUpperCase().substring(0,var_2)+";BYSETPOS=-1"));   
        }
        else
        {
           component.set("v.RecurrencePatternText",(component.get("v.RecurrencePatternText")+";BYMONTHDAY="+special[component.get("v.event.RecurrenceInstance")]));   
        }
    },
    setYearlyRecurr:function(component,special)
    {
		const var_2 = 2;
        component.set("v.RecurrencePatternText",("RRULE:FREQ=YEARLY"+";BYMONTH="+component.get("v.event.RecurrenceMonthOfYear")+";UNTIL="+component.get("v.event.RecurrenceEndDateOnly").replaceAll('-', '')+'T235900Z'));
		
        if(component.find("everyYear1").get("v.checked"))
        {    
			component.set("v.RecurrencePatternText",(component.get("v.RecurrencePatternText")+";BYMONTHDAY="+component.get("v.event.RecurrenceDayOfMonth")));   
        }
        else if(component.find("everyYear2").get("v.checked") && component.get("v.selectedDOYValue") != $A.get("$Label.c.day") && component.get("v.event.RecurrenceInstance") != 'Last')   
        {
            component.set("v.RecurrencePatternText",(component.get("v.RecurrencePatternText")+";BYDAY="+special[component.get("v.event.RecurrenceInstance")]+component.get("v.selectedDOYValue").toUpperCase().substring(0,var_2)));  
        }
        else if(component.find("everyYear2").get("v.checked") && component.get("v.selectedDOYValue") != $A.get("$Label.c.day") && component.get("v.event.RecurrenceInstance") == 'Last')   
        {
            component.set("v.RecurrencePatternText",(component.get("v.RecurrencePatternText")+";BYDAY="+component.get("v.selectedDOYValue").toUpperCase().substring(0,var_2)+";BYSETPOS=-1"));  
        }
        else
        {
            component.set("v.RecurrencePatternText",(component.get("v.RecurrencePatternText")+";BYMONTHDAY="+special[component.get("v.event.RecurrenceInstance")]));   
        }
    },
    
    //Anil M - Start

     initializelookups: function(component)
            {
                component.set('v.selAccount',null);
        	},
    //Anil M - End

    /**
     * Helper method to clear all phase-related values to prevent cross-account contamination
     * @param {Object} component - The Lightning component
     */
    clearPhaseValues: function(component) {
        // Clear component phase attributes
        component.set("v.ast_Phase", null);
        component.set("v.mf_Phase", null);
        component.set("v.astRecommendTarget", []);
        component.set("v.mfRecommendTarget", []);

        // Clear event phase values
        let tempEvent = component.get("v.event");
        if (tempEvent) {
            tempEvent.JJ_JPN_AST_Phase__c = null;
            tempEvent.JJ_JPN_MF_Phase__c = null;
            tempEvent.JJ_JPN_AST_Recommend_Target__c = null;
            tempEvent.JJ_JPN_MF_Recommend_Target__c = null;
            component.set("v.event", tempEvent);
        }
    }
    
})