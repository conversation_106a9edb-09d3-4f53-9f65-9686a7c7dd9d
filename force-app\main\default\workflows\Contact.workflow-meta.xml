<?xml version="1.0" encoding="UTF-8"?>
<Workflow xmlns="http://soap.sforce.com/2006/04/metadata">
    <fieldUpdates>
        <fullName>Checkbox_field</fullName>
        <field>is_Test_User__c</field>
        <literalValue>1</literalValue>
        <name>Checkbox field</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>ClearDate</fullName>
        <field>MA2_ClearLadderDate__c</field>
        <name>ClearDate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Null</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>ClearExpiryDate</fullName>
        <field>MA2_ExpiryDatetoUncheck__c</field>
        <name>ClearExpiryDate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Null</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Contact_Account_Update</fullName>
        <field>MA2_AccountID__c</field>
        <formula>Account.OutletNumber__c</formula>
        <name>Contact Account Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Contact_Mapping</fullName>
        <description>MyAcuvue Contact field update</description>
        <field>MA2_ContactID__c</field>
        <formula>MembershipNo__c</formula>
        <name>Contact Mapping</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>ExpiryDate</fullName>
        <field>MA2_ExpiryDatetoUncheck__c</field>
        <formula>NOW()</formula>
        <name>ExpiryDate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>JJVPRO_registration</fullName>
        <field>JJ_JPN_JJVPROregistration__c</field>
        <literalValue>1</literalValue>
        <name>JJVPRO registration</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>LadderDate</fullName>
        <field>MA2_ClearLadderDate__c</field>
        <formula>NOW()</formula>
        <name>LadderDate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>MA2_ApprovalSentUpdate</fullName>
        <field>MA2_AccessStatus__c</field>
        <literalValue>Sent for Approval</literalValue>
        <name>ApprovalSentUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>MA2_ApprovalUpdate</fullName>
        <field>MA2_AccessStatus__c</field>
        <literalValue>Approved</literalValue>
        <name>ApprovalUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>MA2_ExpiryFlagUpdate</fullName>
        <field>MA2_ExpiryFlag__c</field>
        <literalValue>1</literalValue>
        <name>SFMC Expiry Flag Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>MA2_LadderFlagUpdate</fullName>
        <field>MA2_LadderFlag__c</field>
        <literalValue>1</literalValue>
        <name>Ladder Flag Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>MA2_RejectRequest</fullName>
        <field>MA2_AccessStatus__c</field>
        <literalValue>Rejected</literalValue>
        <name>RejectRequest</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>TwilioSF__Clear_Last_Message_Status</fullName>
        <field>TwilioSF__Last_Message_Status__c</field>
        <name>Clear Last Message Status</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Literal</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>TwilioSF__Clear_Last_Message_Status_Date</fullName>
        <field>TwilioSF__Last_Message_Status_Date__c</field>
        <name>Clear Last Message Status Date</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Null</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <rules>
        <fullName>ClearExpiryDate</fullName>
        <actions>
            <name>ClearExpiryDate</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>Datevalue(MA2_ExpiryDatetoUncheck__c) &lt;  TODAY()</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>ClearLadderDate</fullName>
        <actions>
            <name>ClearDate</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>Datevalue(MA2_ClearLadderDate__c) &lt; TODAY()</formula>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>Expiry Point Change Notification</fullName>
        <actions>
            <name>ExpiryDate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>MA2_ExpiryFlagUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK(MA2_expirynotifydate__c)),  ISBLANK(MA2_ExpiryDatetoUncheck__c), MA2_ApigeeExpiryFlag__c = true  )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>IsTestUser Checkbox Updated</fullName>
        <actions>
            <name>Checkbox_field</name>
            <type>FieldUpdate</type>
        </actions>
        <active>false</active>
        <formula>OR( (AND( (RecordType.Id=&apos;01290000000uId2&apos;), (OR(CONTAINS( UPPER(FirstName), &apos;TEST&apos;) , CONTAINS( UPPER(LastName), &apos;TEST&apos;))), OR(ISPICKVAL(MA2_Country_Code__c, &apos;SGP&apos;), ISPICKVAL(MA2_Country_Code__c, &apos;HKG&apos;), ISPICKVAL(MA2_Country_Code__c, &apos;TWN&apos;), ISPICKVAL(MA2_Country_Code__c, &apos;AUS&apos;)) )), (AND(OR(CONTAINS( UPPER(FirstName), &apos;TEST&apos;) , CONTAINS( UPPER(LastName), &apos;TEST&apos;)),RecordType.Id=&apos;01290000000uId7&apos;)) )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_JJvproRegistration</fullName>
        <actions>
            <name>JJVPRO_registration</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Contact.RecordTypeId</field>
            <operation>equals</operation>
            <value>HCP Contacts</value>
        </criteriaItems>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>Ladder Point Change Notification</fullName>
        <actions>
            <name>LadderDate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>MA2_LadderFlagUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( NOT(ISBLANK(MA2_laddernotifydate__c)),  ISBLANK(MA2_ClearLadderDate__c), MA2_ApigeeLadderFlag__c = true )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>LastExpiryNotifyDate</fullName>
        <actions>
            <name>MA2_ExpiryFlagUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND( ISCHANGED(MA2_ExpiryPoints__c), DATEVALUE(MA2_ExpiryDatetoUncheck__c) &lt;&gt; TODAY()  )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>LastLadderNotifyDate</fullName>
        <actions>
            <name>MA2_LadderFlagUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>AND(  ISCHANGED(MA2_nextLadderChange__c),  DATEVALUE(MA2_ClearLadderDate__c) &lt;&gt; TODAY()  )</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>MyAcuvue Contact Mapping</fullName>
        <actions>
            <name>Contact_Account_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>Contact_Mapping</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Contact.Aws_ContactId__c</field>
            <operation>notEqual</operation>
            <value>Null</value>
        </criteriaItems>
        <criteriaItems>
            <field>Contact.MA2_Country_Code__c</field>
            <operation>equals</operation>
            <value>KOR</value>
        </criteriaItems>
        <description>MyAcuvue Contact Mapping - Used for MyAcuvue Analytics</description>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>TwilioSF__Clear Last Message Status%2FDate</fullName>
        <actions>
            <name>TwilioSF__Clear_Last_Message_Status</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>TwilioSF__Clear_Last_Message_Status_Date</name>
            <type>FieldUpdate</type>
        </actions>
        <active>false</active>
        <formula>1!=1</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
</Workflow>
