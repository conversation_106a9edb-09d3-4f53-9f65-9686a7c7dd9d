<?xml version="1.0" encoding="UTF-8"?>
<Workflow xmlns="http://soap.sforce.com/2006/04/metadata">
    <fieldUpdates>
        <fullName>AprUpdate</fullName>
        <field>JJ_JPN_Apr__c</field>
        <formula>0</formula>
        <name>AprUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>AugUpdate</fullName>
        <field>JJ_JPN_Aug__c</field>
        <formula>0</formula>
        <name>AugUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>DecUpdate</fullName>
        <field>JJ_JPN_Dec__c</field>
        <formula>0</formula>
        <name>DecUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>FebUpdate</fullName>
        <field>JJ_JPN_Feb__c</field>
        <formula>0</formula>
        <name>FebUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>JJ_JPN_AccountAPBehave</fullName>
        <field>JJ_JPN_BehaveAct__c</field>
        <formula>today()</formula>
        <name>Account AP Behave</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>JJ_JPN_AgreementActUpdate</fullName>
        <field>JJ_JPN_AgreementAct__c</field>
        <formula>today()</formula>
        <name>Agreement Act Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>JJ_JPN_FieldUpdatetoApproachAct</fullName>
        <field>JJ_JPN_ApproachAct__c</field>
        <formula>today()</formula>
        <name>FieldUpdate to Approach Act</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>JJ_JPN_TrailActfieldUpdate</fullName>
        <field>JJ_JPN_TrialAct__c</field>
        <formula>today()</formula>
        <name>Trail Act field Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>JJ_JPN_UpdateActionPlanNegoAct</fullName>
        <field>JJ_JPN_NegoAct__c</field>
        <formula>today()</formula>
        <name>Update Action Plan Nego Act</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>JanUpdate</fullName>
        <field>JJ_JPN_Jan__c</field>
        <formula>0</formula>
        <name>JanUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>JulUpdate</fullName>
        <field>JJ_JPN_Jul__c</field>
        <formula>0</formula>
        <name>JulUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>JunUpdate</fullName>
        <field>JJ_JPN_Jun__c</field>
        <formula>0</formula>
        <name>JunUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>MarUpdate</fullName>
        <field>JJ_JPN_Mar__c</field>
        <formula>0</formula>
        <name>MarUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>MayUpdate</fullName>
        <field>JJ_JPN_May__c</field>
        <formula>0</formula>
        <name>MayUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>NovUpdate</fullName>
        <field>JJ_JPN_Nov__c</field>
        <formula>0</formula>
        <name>NovUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>OctUpdate</fullName>
        <field>JJ_JPN_Oct__c</field>
        <formula>0</formula>
        <name>OctUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>SepUpdate</fullName>
        <field>JJ_JPN_Sep__c</field>
        <formula>0</formula>
        <name>SepUpdate</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <rules>
        <fullName>JJ_JPN_ActionPlanPhaseAccountPlan</fullName>
        <actions>
            <name>JJ_JPN_FieldUpdatetoApproachAct</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>ISPICKVAL(JJ_JPN_ActionPlanPhase__c,&apos;Approach&apos;)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_ActionPlanforAP_Canceled</fullName>
        <actions>
            <name>AprUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>FebUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>JanUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>JunUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>MarUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>MayUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>JJ_JPN_ActionPlanforAP__c.JJ_JPN_ActionPlanPhase__c</field>
            <operation>equals</operation>
            <value>Canceled</value>
        </criteriaItems>
        <description>Canceled action for ActionPlan AP</description>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_ActionPlanforAP_canceled2</fullName>
        <actions>
            <name>AugUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>DecUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>JulUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>NovUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>OctUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>SepUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>JJ_JPN_ActionPlanforAP__c.JJ_JPN_ActionPlanPhase__c</field>
            <operation>equals</operation>
            <value>Canceled</value>
        </criteriaItems>
        <description>Canceled action update</description>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_AgreementAccountAP</fullName>
        <actions>
            <name>JJ_JPN_AgreementActUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>ISPICKVAL(JJ_JPN_ActionPlanPhase__c,&apos;Agreement&apos;)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_BehaveAccountAP</fullName>
        <actions>
            <name>JJ_JPN_AccountAPBehave</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>ISPICKVAL(JJ_JPN_ActionPlanPhase__c,&apos;Behave&apos;)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_FieldUpdateforNegoAct</fullName>
        <actions>
            <name>JJ_JPN_UpdateActionPlanNegoAct</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>ISPICKVAL(JJ_JPN_ActionPlanPhase__c,&apos;Nego&apos;)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_TrailAccountAP</fullName>
        <actions>
            <name>JJ_JPN_TrailActfieldUpdate</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>ISPICKVAL(JJ_JPN_ActionPlanPhase__c,&apos;Trial&apos;)</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
</Workflow>
