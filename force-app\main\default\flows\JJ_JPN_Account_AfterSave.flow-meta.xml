<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>After Save flow to clear ShippingCountry field for JPN accounts. This must run after save because other processes set ShippingCountry during before save context.</description>
    <interviewLabel>JJ_JPN Account AfterSave {!$Flow.CurrentDateTime}</interviewLabel>
    <label>JJ_JPN Account AfterSave</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Clear_ShippingCountry</name>
        <label>Clear ShippingCountry</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <inputAssignments>
            <field>ShippingCountry</field>
            <value>
                <stringValue></stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>176</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Clear_ShippingCountry</targetReference>
        </connector>
        <filterFormula>{!$Record.CountryCode__c} = "JPN"</filterFormula>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Draft</status>
</Flow>
