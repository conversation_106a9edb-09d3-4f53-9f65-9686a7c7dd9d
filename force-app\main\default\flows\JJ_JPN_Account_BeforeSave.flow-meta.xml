<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assignment_Clean_Phone</name>
        <label>Clean Phone Number</label>
        <locationX>44</locationX>
        <locationY>431</locationY>
        <assignmentItems>
            <assignToReference>$Record.JJ_JPN_Phone_Update__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Formula_Clean_Phone</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Decision_Person_InCharge_Check</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assignment_Set_Field_Sales</name>
        <label>Set Field Sales Customer Type</label>
        <locationX>308</locationX>
        <locationY>1079</locationY>
        <assignmentItems>
            <assignToReference>$Record.JJ_JPN_CustomerType__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Field Sales(FS)</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Decision_Introduction_Priority_Check</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assignment_Set_Priority_88</name>
        <label>Set Introduction Priority 88</label>
        <locationX>44</locationX>
        <locationY>1295</locationY>
        <assignmentItems>
            <assignToReference>$Record.IntroductionPriority__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>88</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>Assignment_Set_Priority_99</name>
        <label>Set Introduction Priority 99</label>
        <locationX>308</locationX>
        <locationY>1295</locationY>
        <assignmentItems>
            <assignToReference>$Record.IntroductionPriority__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>99</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>Assignment_Set_SAM_RAM</name>
        <label>Set SAM/RAM Customer Type</label>
        <locationX>44</locationX>
        <locationY>1079</locationY>
        <assignmentItems>
            <assignToReference>$Record.JJ_JPN_CustomerType__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>SAM/RAM</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Decision_Introduction_Priority_Check</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assignment_Store_Previous_Person</name>
        <label>Store Previous Person</label>
        <locationX>44</locationX>
        <locationY>863</locationY>
        <assignmentItems>
            <assignToReference>$Record.JJ_JPN_PreviousPersonInchargeName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record__Prior.custGroup__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Decision_Customer_Type_Check</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Decision_Customer_Type_Check</name>
        <label>Customer Type Check</label>
        <locationX>176</locationX>
        <locationY>971</locationY>
        <defaultConnector>
            <targetReference>Decision_Introduction_Priority_Check</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Customer Type Update</defaultConnectorLabel>
        <rules>
            <name>Set_SAM_RAM_Type</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CountryCode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Formula_Contains_SAM_Codes</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_Set_SAM_RAM</targetReference>
            </connector>
            <label>Set SAM/RAM Type</label>
        </rules>
        <rules>
            <name>Set_Field_Sales_Type</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CountryCode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Formula_Contains_SAM_Codes</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_Set_Field_Sales</targetReference>
            </connector>
            <label>Set Field Sales Type</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_Introduction_Priority_Check</name>
        <label>Introduction Priority Check</label>
        <locationX>176</locationX>
        <locationY>1187</locationY>
        <defaultConnectorLabel>No Priority Update</defaultConnectorLabel>
        <rules>
            <name>Set_Priority_88</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CountryCode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Formula_Facility_1_or_3</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.IntroductionPriority__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_Set_Priority_88</targetReference>
            </connector>
            <label>Set Priority 88</label>
        </rules>
        <rules>
            <name>Set_Priority_99</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CountryCode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.JJ_JPN_FacilityInformation__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.IntroductionPriority__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_Set_Priority_99</targetReference>
            </connector>
            <label>Set Priority 99</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_Person_InCharge_Check</name>
        <label>Person In Charge Check</label>
        <locationX>176</locationX>
        <locationY>755</locationY>
        <defaultConnector>
            <targetReference>Decision_Customer_Type_Check</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Person Change</defaultConnectorLabel>
        <rules>
            <name>Person_Changed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CountryCode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.custGroup__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.custGroup__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_Store_Previous_Person</targetReference>
            </connector>
            <label>Person Changed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_Phone_Update_Check</name>
        <label>Phone Update Check</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <defaultConnector>
            <targetReference>Decision_Person_InCharge_Check</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Phone Update Needed</defaultConnectorLabel>
        <rules>
            <name>Phone_Needs_Cleaning</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CountryCode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Phone</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_Clean_Phone</targetReference>
            </connector>
            <label>Phone Needs Cleaning</label>
        </rules>
    </decisions>
    <description>Before Save flow to replace 6 JJ_JPN workflow rules on Account object. Handles
        phone updates, person in charge tracking, customer type updates, and
        introduction priority settings for Japan region accounts.</description>
    <formulas>
        <name>Formula_Clean_Phone</name>
        <dataType>String</dataType>
        <expression>SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE({!$Record.Phone},
            &quot;-&quot;,&quot;&quot;), &quot;+&quot;,&quot;&quot;), &quot;(&quot;,&quot;&quot;),
            &quot;)&quot;,&quot;&quot;), &quot; &quot;,&quot;&quot;)</expression>
    </formulas>
    <formulas>
        <name>Formula_Contains_SAM_Codes</name>
        <dataType>Boolean</dataType>
        <expression>OR(
            CONTAINS({!$Record.JJ_JPN_SAMDESC__c}, &quot;JPD040&quot;),
            CONTAINS({!$Record.JJ_JPN_SAMDESC__c}, &quot;JPD041&quot;),
            CONTAINS({!$Record.JJ_JPN_SAMDESC__c}, &quot;JPD042&quot;)
            )</expression>
    </formulas>
    <formulas>
        <name>Formula_Facility_1_or_3</name>
        <dataType>Boolean</dataType>
        <expression>OR(
            TEXT({!$Record.JJ_JPN_FacilityInformation__c}) = &quot;1&quot;,
            TEXT({!$Record.JJ_JPN_FacilityInformation__c}) = &quot;3&quot;
            )</expression>
    </formulas>
    <interviewLabel>JJ_JPN Account BeforeSave {!$Flow.CurrentDateTime}</interviewLabel>
    <label>JJ_JPN Account BeforeSave</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>176</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Decision_Phone_Update_Check</targetReference>
        </connector>
        <filterFormula>{!$Record.CountryCode__c} = &quot;JPN&quot;</filterFormula>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>Var_Debug_Message</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Flow started</stringValue>
        </value>
    </variables>
    <variables>
        <name>Var_Error_Message</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue></stringValue>
        </value>
    </variables>
    <variables>
        <name>Var_Error_Occurred</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>Var_Record_Id</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Record.Id</elementReference>
        </value>
    </variables>
</Flow>
