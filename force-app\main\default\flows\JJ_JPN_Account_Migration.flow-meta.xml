<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assignment_Clean_Phone</name>
        <label>Clean Phone Number</label>
        <locationX>44</locationX>
        <locationY>431</locationY>
        <assignmentItems>
            <assignToReference>$Record.JJ_JPN_Phone_Update__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Formula_Clean_Phone</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <decisions>
        <name>Decision_Phone_Update_Check</name>
        <label>Phone Update Check</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>No Phone Update Needed</defaultConnectorLabel>
        <rules>
            <name>Phone_Needs_Cleaning</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CountryCode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Phone</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assignment_Clean_Phone</targetReference>
            </connector>
            <label>Phone Needs Cleaning</label>
        </rules>
    </decisions>
    <description>Consolidated flow to replace 7 JJ_JPN workflow rules on Account object. Handles phone updates, country name blanking, person in charge tracking, customer type updates, and introduction priority settings for Japan region accounts.</description>
    <formulas>
        <name>Formula_Clean_Phone</name>
        <dataType>String</dataType>
        <expression>SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE({!$Record.Phone}, &quot;-&quot;,&quot;&quot;), &quot;+&quot;,&quot;&quot;), &quot;(&quot;,&quot;&quot;), &quot;)&quot;,&quot;&quot;), &quot; &quot;,&quot;&quot;)</expression>
    </formulas>
    <interviewLabel>JJ_JPN Account Migration {!$Flow.CurrentDateTime}</interviewLabel>
    <label>JJ_JPN Account Migration</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>176</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Decision_Phone_Update_Check</targetReference>
        </connector>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Draft</status>
</Flow>
