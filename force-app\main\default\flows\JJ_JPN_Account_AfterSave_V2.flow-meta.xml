<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Decision_JPN_Company_Name_Update</name>
        <label>JPN Company Name Update Check</label>
        <locationX>176</locationX>
        <locationY>395</locationY>
        <defaultConnector>
            <targetReference>Update_Clear_ShippingCountry</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Company Name Update</defaultConnectorLabel>
        <rules>
            <name>Account_Code_Changed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CountryCode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Store</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Formula_Account_Code_Changed</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Formula_Prevent_JPN_Company_Name_Recursion</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_JPN_Company_Name_Direct</targetReference>
            </connector>
            <label>Account Code Changed</label>
        </rules>
        <rules>
            <name>Account_Name_Changed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.CountryCode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>JPN</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Store</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Formula_Prevent_JPN_Company_Name_Recursion</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_JPN_Company_Name_Direct</targetReference>
            </connector>
            <label>Account Name Changed</label>
        </rules>
    </decisions>
    <description>After Save flow to clear ShippingCountry field and update JPN Company Name for JPN accounts.
        Includes recursion prevention to avoid CPU timeout issues that occurred with the original Process Builder.
        This must run after save because other processes set ShippingCountry during before save context.</description>
    <formulas>
        <name>Formula_Account_Code_Changed</name>
        <dataType>Boolean</dataType>
        <expression>OR(
            ISCHANGED({!$Record.JJ_JPN_Account_CODE__c}),
            AND(
                ISNEW(),
                NOT(ISBLANK({!$Record.JJ_JPN_Account_CODE__c}))
            )
        )</expression>
    </formulas>
    <formulas>
        <name>Formula_Prevent_JPN_Company_Name_Recursion</name>
        <dataType>Boolean</dataType>
        <expression>AND(
            NOT(ISCHANGED({!$Record.JJ_JPN_JPNCompanyName__c})),
            NOT(ISCHANGED({!$Record.LastModifiedById})),
            OR(
                ISCHANGED({!$Record.Name}),
                ISCHANGED({!$Record.JJ_JPN_Account_CODE__c}),
                ISNEW()
            )
        )</expression>
    </formulas>
    <interviewLabel>JJ_JPN Account AfterSave V2 {!$Flow.CurrentDateTime}</interviewLabel>
    <label>JJ_JPN Account AfterSave V2</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_Clear_ShippingCountry</name>
        <label>Clear ShippingCountry</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <connector>
            <targetReference>Decision_JPN_Company_Name_Update</targetReference>
        </connector>
        <inputAssignments>
            <field>ShippingCountry</field>
            <value>
                <stringValue></stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_JPN_Company_Name_Direct</name>
        <label>Update JPN Company Name Direct</label>
        <locationX>44</locationX>
        <locationY>503</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OutletNumber__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.JJ_JPN_Account_CODE__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>JJ_JPN_JPNCompanyName__c</field>
            <value>
                <elementReference>$Record.Name</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>176</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Clear_ShippingCountry</targetReference>
        </connector>
        <filterFormula>AND(
            {!$Record.CountryCode__c} = "JPN",
            NOT(AND(
                ISCHANGED({!$Record.JJ_JPN_JPNCompanyName__c}),
                NOT(ISCHANGED({!$Record.Name})),
                NOT(ISCHANGED({!$Record.JJ_JPN_Account_CODE__c}))
            ))
        )</filterFormula>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
