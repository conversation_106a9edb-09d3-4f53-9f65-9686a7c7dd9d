<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Update JPN Company Name on Account</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_1_A1</name>
        <label>Update JPN Company Name based on Account CODE</label>
        <locationX>100</locationX>
        <locationY>200</locationY>
        <actionName>SW_Update_JPN_Company_Name_on_Account</actionName>
        <actionType>flow</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>accountId</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>accountId</name>
            <value>
                <elementReference>myVariable_current.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>accountCode</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>accountCode</name>
            <value>
                <elementReference>myVariable_current.JJ_JPN_Account_CODE__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Boolean</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>IsAcctNameChanged</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Boolean</stringValue>
                </value>
            </processMetadataValues>
            <name>IsAcctNameChanged</name>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>SW_Update_JPN_Company_Name_on_Account</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>ActionCallType</name>
            <value>
                <stringValue>flow</stringValue>
            </value>
        </processMetadataValues>
        <processMetadataValues>
            <name>flowSelection</name>
            <value>
                <stringValue>Update JPN Company Name on Account</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_3_A1</name>
        <label>Update JPN Company Name on change of Account Name</label>
        <locationX>300</locationX>
        <locationY>200</locationY>
        <actionName>SW_Update_JPN_Company_Name_on_Account</actionName>
        <actionType>flow</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>Boolean</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>IsAcctNameChanged</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Boolean</stringValue>
                </value>
            </processMetadataValues>
            <name>IsAcctNameChanged</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>acctOutletNumber</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>acctOutletNumber</name>
            <value>
                <elementReference>myVariable_current.OutletNumber__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <processMetadataValues>
                <name>dataType</name>
                <value>
                    <stringValue>String</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>isRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>leftHandSideLabel</name>
                <value>
                    <stringValue>acctName</stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>maxOccurs</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>objectType</name>
                <value>
                    <stringValue></stringValue>
                </value>
            </processMetadataValues>
            <processMetadataValues>
                <name>rightHandSideType</name>
                <value>
                    <stringValue>Reference</stringValue>
                </value>
            </processMetadataValues>
            <name>acctName</name>
            <value>
                <elementReference>myVariable_current.Name</elementReference>
            </value>
        </inputParameters>
        <nameSegment>SW_Update_JPN_Company_Name_on_Account</nameSegment>
    </actionCalls>
    <apiVersion>49.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision</name>
        <label>myDecision</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_1</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_A1</targetReference>
            </connector>
            <label>Account Code is equal to Outlet Number</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision2</name>
        <label>myDecision2</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>formula_myRule_3</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_3_A1</targetReference>
            </connector>
            <label>Is Account Name changed?</label>
        </rules>
    </decisions>
    <description>The JPN Company Name will be updated with Account Name of the record where Account Code is equal to Outlet Number and also on change of Account Name</description>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>AND([Account].CountryCode__c = &apos;JPN&apos;,
[Account].RecordType.DeveloperName = &apos;Store&apos;,
OR(
ISCHANGED([Account].JJ_JPN_Account_CODE__c),
AND(
ISNEW() ,NOT(ISBLANK([Account].JJ_JPN_Account_CODE__c))
)
)
)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_1</name>
        <dataType>Boolean</dataType>
        <expression>AND({!myVariable_current.CountryCode__c} = &apos;JPN&apos;,
{!myVariable_current.RecordType.DeveloperName} = &apos;Store&apos;,
OR(
ISCHANGED({!myVariable_current.JJ_JPN_Account_CODE__c}),
AND(
ISNEW() ,NOT(ISBLANK({!myVariable_current.JJ_JPN_Account_CODE__c}))
)
)
)</expression>
    </formulas>
    <formulas>
        <processMetadataValues>
            <name>originalFormula</name>
            <value>
                <stringValue>AND([Account].CountryCode__c =&apos;JPN&apos;,
[Account].RecordType.DeveloperName = &apos;Store&apos;,
ISCHANGED([Account].Name)
)</stringValue>
            </value>
        </processMetadataValues>
        <name>formula_myRule_3</name>
        <dataType>Boolean</dataType>
        <expression>AND({!myVariable_current.CountryCode__c} =&apos;JPN&apos;,
{!myVariable_current.RecordType.DeveloperName} = &apos;Store&apos;,
ISCHANGED({!myVariable_current.Name})
)</expression>
    </formulas>
    <interviewLabel>SW_Update_JPN_Company_Name-4_InterviewLabel</interviewLabel>
    <label>Update JPN Company Name</label>
    <processMetadataValues>
        <name>ObjectType</name>
        <value>
            <stringValue>Account</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>ObjectVariable</name>
        <value>
            <elementReference>myVariable_current</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OldObjectVariable</name>
        <value>
            <elementReference>myVariable_old</elementReference>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>TriggerType</name>
        <value>
            <stringValue>onAllChanges</stringValue>
        </value>
    </processMetadataValues>
    <processType>Workflow</processType>
    <startElementReference>myDecision</startElementReference>
    <status>Draft</status>
    <variables>
        <name>myVariable_current</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>myVariable_old</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
</Flow>
