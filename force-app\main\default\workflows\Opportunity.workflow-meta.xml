<?xml version="1.0" encoding="UTF-8"?>
<Workflow xmlns="http://soap.sforce.com/2006/04/metadata">
    <fieldUpdates>
        <fullName>Amount</fullName>
        <field>Amount</field>
        <formula>IF(And(NOT(ISPICKVAL(StageName, &quot;Closed Lost&quot;)),OR(ISPICKVAL(StageName ,&quot;Not Started&quot;), (ISPICKVAL(StageName, &quot;Approach&quot;)), (ISPICKVAL(StageName, &quot;Trial&quot;)), 
(ISPICKVAL(StageName, &quot;Approach&quot;)), (ISPICKVAL(StageName, &quot;Nego&quot;)), (ISPICKVAL(StageName, &quot;Agreement&quot;)), 
(ISPICKVAL(StageName, &quot;Behave&quot;)))),J<PERSON>_<PERSON><PERSON>_Jan__c + <PERSON><PERSON>_JPN_Feb__c + JJ_JPN_Mar__c+ J<PERSON>_JPN_Apr__c + JJ_JPN_May__c 
+ JJ_JPN_Jun__c + JJ_JPN_Jul__c + JJ_JPN_Aug__c + JJ_JPN_Sep__c + JJ_JPN_Oct__c + JJ_JPN_Nov__c + JJ_JPN_Dec__c, if(ISPICKVAL(StageName, &quot;Closed Lost&quot;),JJ_JPN_Jan__c + JJ_JPN_Feb__c + JJ_JPN_Mar__c+ JJ_JPN_Apr__c + JJ_JPN_May__c 
+ JJ_JPN_Jun__c + JJ_JPN_Jul__c + JJ_JPN_Aug__c + JJ_JPN_Sep__c + JJ_JPN_Oct__c + JJ_JPN_Nov__c + JJ_JPN_Dec__c,0))</formula>
        <name>Amount</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>true</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Approach_Act</fullName>
        <field>JJ_JPN_ApproachAct__c</field>
        <formula>today()</formula>
        <name>Approach Act</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Apr_Update</fullName>
        <field>JJ_JPN_Apr__c</field>
        <formula>0</formula>
        <name>Apr Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Aug_Update</fullName>
        <field>JJ_JPN_Aug__c</field>
        <formula>0</formula>
        <name>Aug Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>BehavePlan</fullName>
        <field>JJ_JPN_BehavePlan__c</field>
        <formula>CloseDate</formula>
        <name>BehavePlan</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Behave_Act</fullName>
        <field>JJ_JPN_BehaveAct__c</field>
        <formula>today()</formula>
        <name>Behave Act</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Dec_Update</fullName>
        <field>JJ_JPN_Dec__c</field>
        <formula>0</formula>
        <name>Dec Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Feb_Update</fullName>
        <field>JJ_JPN_Feb__c</field>
        <formula>0</formula>
        <name>Feb Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Field_update_Agreement_Act</fullName>
        <field>JJ_JPN_AgreementAct__c</field>
        <formula>today()</formula>
        <name>Field update Agreement Act</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Field_update_Behave_Act</fullName>
        <field>JJ_JPN_BehaveAct__c</field>
        <formula>today()</formula>
        <name>Behave Act</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Jan_update</fullName>
        <field>JJ_JPN_Jan__c</field>
        <formula>0</formula>
        <name>Jan update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Jul_Update</fullName>
        <field>JJ_JPN_Jul__c</field>
        <formula>0</formula>
        <name>Jul Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Jun_Update</fullName>
        <field>JJ_JPN_Jun__c</field>
        <formula>0</formula>
        <name>Jun Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Mar_Update</fullName>
        <field>JJ_JPN_Mar__c</field>
        <formula>0</formula>
        <name>Mar Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>May_Update</fullName>
        <field>JJ_JPN_May__c</field>
        <formula>0</formula>
        <name>May Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Nego_Act</fullName>
        <field>JJ_JPN_NegoAct__c</field>
        <formula>today()</formula>
        <name>Nego Act</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Nov_Update</fullName>
        <field>JJ_JPN_Nov__c</field>
        <formula>0</formula>
        <name>Nov Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Oct_Update</fullName>
        <field>JJ_JPN_Oct__c</field>
        <formula>0</formula>
        <name>Oct Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Sep_Update</fullName>
        <field>JJ_JPN_Sep__c</field>
        <formula>0</formula>
        <name>Sep Update</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <fieldUpdates>
        <fullName>Trial_Act</fullName>
        <field>JJ_JPN_TrialAct__c</field>
        <formula>today()</formula>
        <name>Trial Act</name>
        <notifyAssignee>false</notifyAssignee>
        <operation>Formula</operation>
        <protected>false</protected>
        <reevaluateOnChange>false</reevaluateOnChange>
    </fieldUpdates>
    <rules>
        <fullName>JJ_JPN_Agreement Act</fullName>
        <actions>
            <name>Field_update_Agreement_Act</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Opportunity.StageName</field>
            <operation>equals</operation>
            <value>Agreement</value>
        </criteriaItems>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_ApproachAct</fullName>
        <actions>
            <name>Approach_Act</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Opportunity.StageName</field>
            <operation>equals</operation>
            <value>Approach</value>
        </criteriaItems>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_BehaveAct%5F%5Fc</fullName>
        <actions>
            <name>Field_update_Behave_Act</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Opportunity.StageName</field>
            <operation>equals</operation>
            <value>Behave</value>
        </criteriaItems>
        <triggerType>onCreateOrTriggeringUpdate</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_BehavePlan</fullName>
        <actions>
            <name>BehavePlan</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>true</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_Cancelled</fullName>
        <actions>
            <name>Apr_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>Aug_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>Feb_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>Jan_update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>Jul_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>Jun_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>Mar_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>May_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>Oct_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>Sep_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Opportunity.StageName</field>
            <operation>equals</operation>
            <value>Canceled</value>
        </criteriaItems>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_Cancelled2</fullName>
        <actions>
            <name>Dec_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <actions>
            <name>Nov_Update</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Opportunity.StageName</field>
            <operation>equals</operation>
            <value>Canceled</value>
        </criteriaItems>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_Nego Act</fullName>
        <actions>
            <name>Nego_Act</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Opportunity.StageName</field>
            <operation>equals</operation>
            <value>Nego</value>
        </criteriaItems>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_OpptyAmount</fullName>
        <actions>
            <name>Amount</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <formula>true</formula>
        <triggerType>onAllChanges</triggerType>
    </rules>
    <rules>
        <fullName>JJ_JPN_Trial Act</fullName>
        <actions>
            <name>Trial_Act</name>
            <type>FieldUpdate</type>
        </actions>
        <active>true</active>
        <criteriaItems>
            <field>Opportunity.StageName</field>
            <operation>equals</operation>
            <value>Trial</value>
        </criteriaItems>
        <triggerType>onAllChanges</triggerType>
    </rules>
</Workflow>
