<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>Migrated from the: JJ_JPN_CountryNameblank workflow rule
Workflow rule description: It will update Country Name as blank for JPN records&quot;</description>
    <environments>Default</environments>
    <label>JJ_JPN_CountryNameblank</label>
    <migratedFromWorkflowRuleName>JJ_JPN_CountryNameblank</migratedFromWorkflowRuleName>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>mainUpdate</name>
        <label>mainUpdate</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>ShippingCountry</field>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>mainUpdate</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ShippingCountry</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>CountryCode__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>JPN</stringValue>
            </value>
        </filters>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
