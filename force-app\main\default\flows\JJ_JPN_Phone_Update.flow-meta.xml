<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>Migrated from the: JJ_JPN Phone Update workflow rule
Workflow rule description: JJ_JPN Phone Update&quot;</description>
    <formulas>
        <name>JJ_JPN_Phone_UpdateFormula</name>
        <dataType>String</dataType>
        <expression>SUBSTITUTE( SUBSTITUTE( SUBSTITUTE( SUBSTITUTE( SUBSTITUTE( $Record.Phone, &quot;-&quot;,&quot;&quot;), &quot;+&quot;,&quot;&quot;), &quot;(&quot;,&quot;&quot;), &quot;)&quot;,&quot;&quot;), &quot; &quot;,&quot;&quot;)</expression>
    </formulas>
    <label>JJ_JPN Phone Update</label>
    <migratedFromWorkflowRuleName>JJ_JPN Phone Update</migratedFromWorkflowRuleName>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>mainUpdate</name>
        <label>mainUpdate</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>JJ_JPN_Phone_Update__c</field>
            <value>
                <elementReference>JJ_JPN_Phone_UpdateFormula</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>mainUpdate</targetReference>
        </connector>
        <filterFormula>{!$Record.CountryCode__c} = &apos;JPN&apos;</filterFormula>
        <object>Account</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
